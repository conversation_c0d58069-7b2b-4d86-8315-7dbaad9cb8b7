# Interactive telnet script for switch connection
param(
    [string]$SwitchIP = "**********",
    [int]$Port = 23
)

Write-Host "=== Interactive Telnet Connection to Switch ===" -ForegroundColor Green
Write-Host "Connecting to: $SwitchIP on port $Port" -ForegroundColor Yellow

try {
    # Create TCP client
    $client = New-Object System.Net.Sockets.TcpClient
    $client.ReceiveTimeout = 10000
    $client.SendTimeout = 5000
    
    # Connect to switch
    $client.Connect($SwitchIP, $Port)
    $stream = $client.GetStream()
    
    # Create reader and writer with proper encoding
    $encoding = [System.Text.Encoding]::ASCII
    $reader = New-Object System.IO.StreamReader($stream, $encoding)
    $writer = New-Object System.IO.StreamWriter($stream, $encoding)
    $writer.AutoFlush = $true
    
    Write-Host "Connected successfully!" -ForegroundColor Green
    Write-Host "Waiting for initial response..." -ForegroundColor Yellow
    
    # Read initial response with timeout
    $buffer = New-Object byte[] 1024
    $response = ""
    $timeout = 0
    
    while ($timeout -lt 30) {  # 3 seconds timeout
        if ($stream.DataAvailable) {
            $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
            $response += $encoding.GetString($buffer, 0, $bytesRead)
            if ($response -match "Username:" -or $response -match "login:" -or $response -match "Password:" -or $response -match ">" -or $response -match "#") {
                break
            }
        }
        Start-Sleep -Milliseconds 100
        $timeout++
    }
    
    Write-Host "Initial response received:" -ForegroundColor Cyan
    Write-Host $response -ForegroundColor White
    
    # Interactive session
    Write-Host "`n=== Starting Interactive Session ===" -ForegroundColor Green
    Write-Host "You can now type commands. Type 'QUIT' to exit." -ForegroundColor Yellow
    Write-Host "Suggested commands to find IPs containing 10.101.1:" -ForegroundColor Yellow
    Write-Host "  - show arp | include 10.101.1" -ForegroundColor Cyan
    Write-Host "  - show ip arp | include 10.101.1" -ForegroundColor Cyan
    Write-Host "  - show ip interface brief | include 10.101.1" -ForegroundColor Cyan
    Write-Host "  - show arp (then manually look for 10.101.1)" -ForegroundColor Cyan
    Write-Host ""
    
    while ($true) {
        # Read any pending data from switch
        $switchResponse = ""
        $readTimeout = 0
        while ($readTimeout -lt 10 -and $stream.DataAvailable) {
            $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
            $switchResponse += $encoding.GetString($buffer, 0, $bytesRead)
            Start-Sleep -Milliseconds 50
            $readTimeout++
        }
        
        if ($switchResponse.Length -gt 0) {
            Write-Host $switchResponse -ForegroundColor White -NoNewline
        }
        
        # Get user input
        $userInput = Read-Host
        
        if ($userInput.ToUpper() -eq "QUIT") {
            break
        }
        
        # Send command to switch
        $writer.WriteLine($userInput)
        
        # Wait a bit for response
        Start-Sleep -Milliseconds 500
    }
    
} catch {
    Write-Error "Error: $($_.Exception.Message)"
} finally {
    if ($client) {
        $client.Close()
    }
    Write-Host "`nConnection closed." -ForegroundColor Green
}

Write-Host "`n=== Session Summary ===" -ForegroundColor Green
Write-Host "To find IP addresses containing 10.101.1, you should have used commands like:" -ForegroundColor Yellow
Write-Host "  - show arp | include 10.101.1" -ForegroundColor Cyan
Write-Host "  - show ip arp | include 10.101.1" -ForegroundColor Cyan
Write-Host "  - show mac-address-table | include 10.101.1" -ForegroundColor Cyan
