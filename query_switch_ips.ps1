# PowerShell script to query switch for IPs containing 10.101. and save to file
param(
    [string]$SwitchIP = "**********",
    [int]$Port = 23,
    [string]$Username = "fssz",
    [string]$Password = "fssz@2016",
    [string]$OutputFile = "switch_ips_10.101.txt"
)

Write-Host "=== Querying Switch for IPs containing 10.101. ===" -ForegroundColor Green
Write-Host "Switch: $SwitchIP" -ForegroundColor Yellow
Write-Host "Output file: $OutputFile" -ForegroundColor Yellow

$allResults = @()
$timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

try {
    # Create TCP client
    $client = New-Object System.Net.Sockets.TcpClient
    $client.ReceiveTimeout = 10000
    $client.SendTimeout = 5000
    
    # Connect to switch
    Write-Host "Connecting to switch..." -ForegroundColor Yellow
    $client.Connect($SwitchIP, $Port)
    $stream = $client.GetStream()
    
    # Create reader and writer
    $encoding = [System.Text.Encoding]::ASCII
    $reader = New-Object System.IO.StreamReader($stream, $encoding)
    $writer = New-Object System.IO.StreamWriter($stream, $encoding)
    $writer.AutoFlush = $true
    
    # Function to read available data with timeout
    function Read-SwitchData {
        param([int]$TimeoutSeconds = 5)
        
        $buffer = New-Object byte[] 4096
        $response = ""
        $timeout = 0
        $maxTimeout = $TimeoutSeconds * 10
        
        while ($timeout -lt $maxTimeout) {
            if ($stream.DataAvailable) {
                $bytesRead = $stream.Read($buffer, 0, $buffer.Length)
                $response += $encoding.GetString($buffer, 0, $bytesRead)
                
                # Check for common prompts
                if ($response -match "Username:" -or $response -match "Password:" -or $response -match "#\s*$" -or $response -match ">\s*$") {
                    break
                }
            }
            Start-Sleep -Milliseconds 100
            $timeout++
        }
        return $response
    }
    
    # Read initial response
    Write-Host "Reading initial response..." -ForegroundColor Yellow
    $response = Read-SwitchData -TimeoutSeconds 3
    Write-Host "Initial: $($response.Substring(0, [Math]::Min(50, $response.Length)))" -ForegroundColor Cyan
    
    # Login process
    if ($response -match "Username:") {
        Write-Host "Sending username..." -ForegroundColor Yellow
        $writer.WriteLine($Username)
        $response = Read-SwitchData -TimeoutSeconds 3
    }
    
    if ($response -match "Password:") {
        Write-Host "Sending password..." -ForegroundColor Yellow
        $writer.WriteLine($Password)
        $response = Read-SwitchData -TimeoutSeconds 5
    }
    
    # Check if we're in user mode and need to enter privileged mode
    if ($response -match ">\s*$") {
        Write-Host "Entering privileged mode..." -ForegroundColor Yellow
        $writer.WriteLine("enable")
        $response = Read-SwitchData -TimeoutSeconds 3
        
        if ($response -match "Password:") {
            $writer.WriteLine($Password)
            $response = Read-SwitchData -TimeoutSeconds 3
        }
    }
    
    Write-Host "Login successful! Current prompt: $($response.Split("`n")[-1])" -ForegroundColor Green
    
    # Query 1: ARP table for 10.101.
    Write-Host "`nQuerying ARP table for IPs containing 10.101...." -ForegroundColor Yellow
    $writer.WriteLine("show arp | include 10.101.")
    $arpResponse = Read-SwitchData -TimeoutSeconds 5
    
    $allResults += "=== ARP Table Query (show arp | include 10.101.) ==="
    $allResults += "Timestamp: $timestamp"
    $allResults += "Switch: $SwitchIP"
    $allResults += ""
    $allResults += $arpResponse.Split("`n") | Where-Object { $_ -match "10\.101\." -and $_ -notmatch "show arp" }
    $allResults += ""
    
    # Query 2: IP interface brief for 10.101.
    Write-Host "Querying interface IPs containing 10.101...." -ForegroundColor Yellow
    $writer.WriteLine("show ip interface brief | include 10.101.")
    $intResponse = Read-SwitchData -TimeoutSeconds 5
    
    $allResults += "=== Interface IP Query (show ip interface brief | include 10.101.) ==="
    $allResults += $intResponse.Split("`n") | Where-Object { $_ -match "10\.101\." -and $_ -notmatch "show ip interface" }
    $allResults += ""
    
    # Query 3: Get full ARP table and filter locally for more comprehensive results
    Write-Host "Getting full ARP table for comprehensive search..." -ForegroundColor Yellow
    $writer.WriteLine("show arp")
    $fullArpResponse = Read-SwitchData -TimeoutSeconds 10
    
    $allResults += "=== Full ARP Table Filtered for 10.101. ==="
    $filteredArp = $fullArpResponse.Split("`n") | Where-Object { $_ -match "10\.101\." }
    $allResults += $filteredArp
    $allResults += ""
    
    # Query 4: Try to get VLAN information
    Write-Host "Querying VLAN information..." -ForegroundColor Yellow
    $writer.WriteLine("show vlan brief | include 10.101.")
    $vlanResponse = Read-SwitchData -TimeoutSeconds 5
    
    if ($vlanResponse -match "10\.101\.") {
        $allResults += "=== VLAN Information ==="
        $allResults += $vlanResponse.Split("`n") | Where-Object { $_ -match "10\.101\." }
        $allResults += ""
    }
    
    # Extract unique IP addresses
    $uniqueIPs = @()
    foreach ($line in $allResults) {
        if ($line -match "(\b10\.101\.\d+\.\d+\b)") {
            $ip = $matches[1]
            if ($uniqueIPs -notcontains $ip) {
                $uniqueIPs += $ip
            }
        }
    }
    
    # Add summary
    $summary = @()
    $summary += "=== SUMMARY ==="
    $summary += "Query Date: $timestamp"
    $summary += "Switch: $SwitchIP"
    $summary += "Total unique IPs found containing 10.101.: $($uniqueIPs.Count)"
    $summary += ""
    $summary += "Unique IP Addresses:"
    foreach ($ip in ($uniqueIPs | Sort-Object)) {
        $summary += "  - $ip"
    }
    $summary += ""
    $summary += "=== DETAILED RESULTS ==="
    $summary += ""
    
    $finalResults = $summary + $allResults
    
    # Save to file
    $finalResults | Out-File -FilePath $OutputFile -Encoding UTF8
    
    Write-Host "`n=== Results Summary ===" -ForegroundColor Green
    Write-Host "Found $($uniqueIPs.Count) unique IP addresses containing 10.101." -ForegroundColor Yellow
    Write-Host "Results saved to: $OutputFile" -ForegroundColor Green
    
    Write-Host "`nUnique IPs found:" -ForegroundColor Cyan
    foreach ($ip in ($uniqueIPs | Sort-Object)) {
        Write-Host "  - $ip" -ForegroundColor White
    }
    
    # Exit gracefully
    $writer.WriteLine("exit")
    
} catch {
    Write-Error "Error: $($_.Exception.Message)"
    $errorMsg = "Error occurred at $timestamp : $($_.Exception.Message)"
    $errorMsg | Out-File -FilePath $OutputFile -Append -Encoding UTF8
} finally {
    if ($client) {
        $client.Close()
    }
    Write-Host "`nConnection closed." -ForegroundColor Green
}

Write-Host "`nScript completed. Check $OutputFile for detailed results." -ForegroundColor Green
