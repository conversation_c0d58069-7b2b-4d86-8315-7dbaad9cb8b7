# PowerShell script to connect to switch via TCP and query IP addresses
param(
    [string]$SwitchIP = "**********",
    [int]$Port = 23,
    [string]$Username = "fssz",
    [string]$Password = "fssz@2016"
)

try {
    Write-Host "Connecting to switch $SwitchIP on port $Port..."
    
    # Create TCP client
    $client = New-Object System.Net.Sockets.TcpClient
    $client.ReceiveTimeout = 5000
    $client.SendTimeout = 5000
    
    # Connect to switch
    $client.Connect($SwitchIP, $Port)
    $stream = $client.GetStream()
    
    # Create reader and writer
    $reader = New-Object System.IO.StreamReader($stream, [System.Text.Encoding]::ASCII)
    $writer = New-Object System.IO.StreamWriter($stream, [System.Text.Encoding]::ASCII)
    $writer.AutoFlush = $true
    
    # Function to read available data
    function Read-AvailableData {
        $data = ""
        $timeout = 0
        while ($timeout -lt 50) {  # Wait up to 5 seconds
            if ($stream.DataAvailable) {
                $char = $reader.Read()
                if ($char -ne -1) {
                    $data += [char]$char
                }
            } else {
                Start-Sleep -Milliseconds 100
                $timeout++
            }
            # Break if we see a prompt
            if ($data -match ">" -or $data -match "#" -or $data -match "login:" -or $data -match "Password:") {
                break
            }
        }
        return $data
    }
    
    # Read initial response
    Write-Host "Reading initial response..."
    $response = Read-AvailableData
    Write-Host "Initial response: $response"
    
    # Send username if login prompt appears
    if ($response -match "login:" -or $response -match "Username:") {
        Write-Host "Sending username: $Username"
        $writer.WriteLine($Username)
        $response = Read-AvailableData
        Write-Host "After username: $response"
    }
    
    # Send password if password prompt appears
    if ($response -match "Password:") {
        Write-Host "Sending password..."
        $writer.WriteLine($Password)
        $response = Read-AvailableData
        Write-Host "After password: $response"
    }
    
    # Try to enter privileged mode
    Write-Host "Attempting to enter privileged mode..."
    $writer.WriteLine("enable")
    $response = Read-AvailableData
    Write-Host "After enable: $response"
    
    # If password required for enable mode
    if ($response -match "Password:") {
        $writer.WriteLine($Password)
        $response = Read-AvailableData
        Write-Host "After enable password: $response"
    }
    
    # Query ARP table for IP addresses containing 10.101.
    Write-Host "`nQuerying ARP table for IPs containing 10.101...."
    $writer.WriteLine("show arp | include 10.101.")
    Start-Sleep -Seconds 3
    $arpResponse = Read-AvailableData
    Write-Host "ARP table results:"
    Write-Host $arpResponse

    # Query IP interface brief
    Write-Host "`nQuerying interface IPs containing 10.101...."
    $writer.WriteLine("show ip interface brief | include 10.101.")
    Start-Sleep -Seconds 3
    $intResponse = Read-AvailableData
    Write-Host "Interface IP results:"
    Write-Host $intResponse
    
    # Try alternative commands for different switch types
    Write-Host "`nTrying alternative commands..."
    
    # For Cisco switches
    $writer.WriteLine("show ip arp | include 10.101.1")
    Start-Sleep -Seconds 2
    $ciscoArp = Read-AvailableData
    if ($ciscoArp.Trim() -ne "") {
        Write-Host "Cisco ARP results:"
        Write-Host $ciscoArp
    }
    
    # For HP/Aruba switches
    $writer.WriteLine("show arp | include 10.101.1")
    Start-Sleep -Seconds 2
    $hpArp = Read-AvailableData
    if ($hpArp.Trim() -ne "") {
        Write-Host "HP/Aruba ARP results:"
        Write-Host $hpArp
    }
    
    # Show all IP addresses and filter locally
    Write-Host "`nGetting all ARP entries to filter locally..."
    $writer.WriteLine("show arp")
    Start-Sleep -Seconds 3
    $allArp = Read-AvailableData
    
    # Filter for 10.101.1 addresses
    $filteredLines = $allArp -split "`n" | Where-Object { $_ -match "10\.101\.1" }
    if ($filteredLines.Count -gt 0) {
        Write-Host "Found IP addresses containing 10.101.1:"
        foreach ($line in $filteredLines) {
            Write-Host $line
        }
    } else {
        Write-Host "No IP addresses containing 10.101.1 found in ARP table"
    }
    
    # Exit gracefully
    $writer.WriteLine("exit")
    
} catch {
    Write-Error "Error connecting to switch: $($_.Exception.Message)"
} finally {
    if ($client) {
        $client.Close()
    }
    Write-Host "Connection closed."
}
